"""
Ultra-Fast Conversation Service for the AI Companion System.
Optimized for sub-second response times with advanced caching and async processing.
"""

import asyncio
import time
import logging
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
from datetime import datetime, timezone
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor, as_completed
import threading

from models import (
    EmotionType, InteractionType, EmotionalState, MemoryEntry, UserProfile
)
from conversation_service import ConversationContext
from memory_service import AdvancedMemoryService
from gemini_service import OptimizedGeminiService
from advanced_emotional_intelligence import AdvancedEmotionalIntelligence
from learning_service import LearningService
from storage_service import PersistentStorageService

logger = logging.getLogger(__name__)

class UltraFastConversationService:
    """
    Ultra-optimized conversation service designed for sub-second response times.
    
    Features:
    - Multi-level caching (response, context, memory)
    - Parallel processing of AI components
    - Predictive pre-loading
    - Intelligent response streaming
    - Memory-efficient operations
    """
    
    def __init__(self):
        """Initialize the ultra-fast conversation service."""
        # Core services
        self.memory_service = AdvancedMemoryService()
        self.gemini_service = OptimizedGeminiService()
        self.emotional_intelligence = AdvancedEmotionalIntelligence(self.gemini_service)
        self.learning_service = LearningService(self.memory_service, self.gemini_service)
        self.storage_service = PersistentStorageService()
        
        # Ultra-fast caching system
        self.response_cache: Dict[str, str] = {}
        self.context_cache: Dict[str, ConversationContext] = {}
        self.memory_cache: Dict[str, List[MemoryEntry]] = {}
        self.emotional_cache: Dict[str, Any] = {}
        
        # Performance optimization
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        self.cache_lock = threading.RLock()
        self.active_conversations: Dict[str, ConversationContext] = {}
        
        # Performance metrics
        self.response_times: deque = deque(maxlen=100)
        self.cache_hit_rates: Dict[str, float] = defaultdict(float)
        self.total_requests = 0
        
        # Predictive pre-loading
        self.preload_queue: asyncio.Queue = asyncio.Queue()
        self.preload_task = None
        
        # Start background tasks
        asyncio.create_task(self._start_background_tasks())
    
    async def _start_background_tasks(self):
        """Start background optimization tasks."""
        self.preload_task = asyncio.create_task(self._preload_worker())
    
    async def _preload_worker(self):
        """Background worker for predictive pre-loading."""
        while True:
            try:
                user_id = await self.preload_queue.get()
                await self._preload_user_context(user_id)
                self.preload_queue.task_done()
            except Exception as e:
                logger.error(f"Preload worker error: {e}")
                await asyncio.sleep(1)
    
    async def _preload_user_context(self, user_id: str):
        """Preload user context for faster responses."""
        try:
            # Preload recent memories
            if user_id not in self.memory_cache:
                memories = await self.memory_service.get_relevant_memories(
                    user_id, "general context", limit=10
                )
                with self.cache_lock:
                    self.memory_cache[user_id] = memories
            
            # Preload user profile
            if user_id not in self.context_cache:
                profile = await self.storage_service.get_user_profile(user_id)
                if profile:
                    context = ConversationContext(
                        user_id=user_id,
                        conversation_id=f"preload_{user_id}_{int(time.time())}",
                        user_profile=profile
                    )
                    with self.cache_lock:
                        self.context_cache[user_id] = context
                        
        except Exception as e:
            logger.error(f"Error preloading context for {user_id}: {e}")
    
    def _get_cache_key(self, user_id: str, message: str, context_hash: str = "") -> str:
        """Generate cache key for response caching."""
        content = f"{user_id}:{message}:{context_hash}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_context_hash(self, context: ConversationContext) -> str:
        """Generate hash for conversation context."""
        context_data = {
            'recent_emotions': [e.value for e in context.conversation_history[-3:]] if context.conversation_history else [],
            'user_preferences': context.user_profile.preferences if context.user_profile else {},
            'conversation_length': len(context.conversation_history)
        }
        return hashlib.md5(json.dumps(context_data, sort_keys=True).encode()).hexdigest()
    
    async def process_message_ultra_fast(
        self, 
        user_id: str, 
        message: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, float, Dict[str, Any]]:
        """
        Process message with ultra-fast optimization.
        
        Returns:
            Tuple of (response, processing_time, metrics)
        """
        start_time = time.time()
        self.total_requests += 1
        
        try:
            # Step 1: Get or create conversation context (parallel with cache check)
            context_task = asyncio.create_task(self._get_conversation_context(user_id))
            
            # Step 2: Check response cache
            conversation_context = await context_task
            context_hash = self._get_context_hash(conversation_context)
            cache_key = self._get_cache_key(user_id, message, context_hash)
            
            # Cache hit - ultra fast path
            if cache_key in self.response_cache:
                response = self.response_cache[cache_key]
                processing_time = time.time() - start_time
                self.response_times.append(processing_time)
                self.cache_hit_rates['response'] += 1
                
                return response, processing_time, {
                    'cache_hit': True,
                    'processing_time': processing_time,
                    'source': 'cache'
                }
            
            # Step 3: Parallel processing of AI components
            tasks = {
                'emotional_analysis': self._analyze_emotion_fast(user_id, message),
                'memory_retrieval': self._retrieve_memories_fast(user_id, message),
                'context_update': self._update_context_fast(conversation_context, message)
            }
            
            # Execute all tasks in parallel
            results = await asyncio.gather(*tasks.values(), return_exceptions=True)
            emotional_insight = results[0] if not isinstance(results[0], Exception) else None
            relevant_memories = results[1] if not isinstance(results[1], Exception) else []
            updated_context = results[2] if not isinstance(results[2], Exception) else conversation_context
            
            # Step 4: Generate response with fallback strategy
            response = await self._generate_response_fast(
                updated_context, message, emotional_insight, relevant_memories
            )
            
            # Step 5: Cache response and update metrics
            with self.cache_lock:
                if len(self.response_cache) < 1000:  # Limit cache size
                    self.response_cache[cache_key] = response
            
            # Step 6: Background tasks (non-blocking)
            asyncio.create_task(self._background_updates(
                user_id, message, response, emotional_insight, relevant_memories
            ))
            
            processing_time = time.time() - start_time
            self.response_times.append(processing_time)
            
            return response, processing_time, {
                'cache_hit': False,
                'processing_time': processing_time,
                'emotional_insight': emotional_insight.primary_emotion.value if emotional_insight else 'unknown',
                'memory_count': len(relevant_memories),
                'source': 'generated'
            }
            
        except Exception as e:
            logger.error(f"Error in ultra-fast processing: {e}")
            processing_time = time.time() - start_time
            
            # Emergency fallback
            fallback_response = self._get_emergency_fallback(message)
            return fallback_response, processing_time, {
                'cache_hit': False,
                'processing_time': processing_time,
                'error': str(e),
                'source': 'fallback'
            }
    
    async def _get_conversation_context(self, user_id: str) -> ConversationContext:
        """Get conversation context with caching."""
        # Check cache first
        if user_id in self.context_cache:
            return self.context_cache[user_id]
        
        # Load from storage
        if user_id in self.active_conversations:
            context = self.active_conversations[user_id]
        else:
            # Create new context
            profile = await self.storage_service.get_user_profile(user_id)
            context = ConversationContext(
                user_id=user_id,
                conversation_id=f"fast_{user_id}_{int(time.time())}",
                user_profile=profile
            )
            self.active_conversations[user_id] = context
        
        # Cache for future use
        with self.cache_lock:
            self.context_cache[user_id] = context
        
        return context
    
    async def _analyze_emotion_fast(self, user_id: str, message: str):
        """Fast emotional analysis with caching."""
        cache_key = f"emotion_{user_id}_{hashlib.md5(message.encode()).hexdigest()}"
        
        if cache_key in self.emotional_cache:
            self.cache_hit_rates['emotion'] += 1
            return self.emotional_cache[cache_key]
        
        try:
            insight = await self.emotional_intelligence.analyze_emotional_state(
                user_id, message, {'fast_mode': True}
            )
            
            # Cache result
            if len(self.emotional_cache) < 500:
                self.emotional_cache[cache_key] = insight
            
            return insight
        except Exception as e:
            logger.error(f"Fast emotion analysis error: {e}")
            return None
    
    async def _retrieve_memories_fast(self, user_id: str, message: str) -> List[MemoryEntry]:
        """Fast memory retrieval with caching."""
        cache_key = f"memory_{user_id}_{hashlib.md5(message.encode()).hexdigest()[:8]}"
        
        if cache_key in self.memory_cache:
            self.cache_hit_rates['memory'] += 1
            return self.memory_cache[cache_key]
        
        try:
            memories = await self.memory_service.get_relevant_memories(
                user_id, message, limit=5  # Reduced for speed
            )
            
            # Cache result
            if len(self.memory_cache) < 200:
                self.memory_cache[cache_key] = memories
            
            return memories
        except Exception as e:
            logger.error(f"Fast memory retrieval error: {e}")
            return []
    
    async def _update_context_fast(self, context: ConversationContext, message: str) -> ConversationContext:
        """Fast context update."""
        # Add message to history (keep only recent messages for speed)
        context.conversation_history.append({
            'role': 'user',
            'content': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
        
        # Keep only last 10 messages for performance
        if len(context.conversation_history) > 10:
            context.conversation_history = context.conversation_history[-10:]
        
        return context

    async def _generate_response_fast(
        self,
        context: ConversationContext,
        message: str,
        emotional_insight: Any,
        memories: List[MemoryEntry]
    ) -> str:
        """Generate response with speed optimization."""
        try:
            # Use simplified prompt for speed
            prompt = self._create_fast_prompt(context, message, emotional_insight, memories)

            # Generate with timeout
            response_task = asyncio.create_task(
                self.gemini_service.generate_response_async(prompt)
            )

            try:
                response = await asyncio.wait_for(response_task, timeout=2.0)
                return response
            except asyncio.TimeoutError:
                logger.warning("Response generation timeout, using fallback")
                return self._get_contextual_fallback(message, emotional_insight)

        except Exception as e:
            logger.error(f"Fast response generation error: {e}")
            return self._get_contextual_fallback(message, emotional_insight)

    def _create_fast_prompt(
        self,
        context: ConversationContext,
        message: str,
        emotional_insight: Any,
        memories: List[MemoryEntry]
    ) -> str:
        """Create optimized prompt for fast generation."""
        # Simplified prompt for speed
        emotion = emotional_insight.primary_emotion.value if emotional_insight else "neutral"
        intensity = emotional_insight.emotional_intensity if emotional_insight else 0.5

        recent_context = ""
        if context.conversation_history:
            last_exchange = context.conversation_history[-1:]
            recent_context = f"Recent: {last_exchange[0].get('content', '')}" if last_exchange else ""

        memory_context = ""
        if memories:
            memory_context = f"Relevant: {memories[0].content[:100]}..." if memories else ""

        prompt = f"""Respond as an empathetic AI companion. Be concise but caring.

User emotion: {emotion} (intensity: {intensity:.1f})
{recent_context}
{memory_context}

User: {message}

Respond naturally and supportively in 1-2 sentences:"""

        return prompt

    def _get_contextual_fallback(self, message: str, emotional_insight: Any) -> str:
        """Get contextual fallback response."""
        message_lower = message.lower()

        # Emotional context fallbacks
        if emotional_insight and emotional_insight.primary_emotion:
            emotion = emotional_insight.primary_emotion.value
            intensity = emotional_insight.emotional_intensity

            if emotion == "sadness" and intensity > 0.6:
                return "I can hear the sadness in your words. I'm here with you through this difficult moment."
            elif emotion == "anxiety" or emotion == "fear":
                return "I sense you're feeling anxious. Take a deep breath - you're not alone in this."
            elif emotion == "anger" and intensity > 0.5:
                return "I can feel your frustration. It's okay to feel angry - let's work through this together."
            elif emotion == "joy":
                return "I love hearing the happiness in your message! Tell me more about what's bringing you joy."

        # Content-based fallbacks
        if any(word in message_lower for word in ["stressed", "overwhelmed", "pressure"]):
            return "That sounds really overwhelming. I'm here to listen and help you process these feelings."
        elif any(word in message_lower for word in ["excited", "happy", "great", "amazing"]):
            return "That's wonderful to hear! I'd love to share in your excitement."
        elif any(word in message_lower for word in ["tired", "exhausted", "drained"]):
            return "It sounds like you're really tired. Rest is so important - how can I support you right now?"

        # Default empathetic response
        return "I'm here and listening. Would you like to tell me more about what's on your mind?"

    def _get_emergency_fallback(self, message: str) -> str:
        """Emergency fallback for system errors."""
        return "I'm here for you, even though I'm having some technical difficulties. How are you feeling right now?"

    async def _background_updates(
        self,
        user_id: str,
        message: str,
        response: str,
        emotional_insight: Any,
        memories: List[MemoryEntry]
    ):
        """Perform background updates without blocking response."""
        try:
            # Update conversation history
            if user_id in self.active_conversations:
                context = self.active_conversations[user_id]
                context.conversation_history.append({
                    'role': 'assistant',
                    'content': response,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })

            # Create memory entry (background)
            if emotional_insight:
                await self.memory_service.create_episodic_memory(
                    user_id=user_id,
                    title=f"Conversation: {message[:30]}...",
                    description=f"User: {message}\nAI: {response}",
                    emotional_context={
                        'primary_emotion': emotional_insight.primary_emotion,
                        'intensity': emotional_insight.emotional_intensity
                    }
                )

            # Update psychological profile (background)
            if emotional_insight:
                await self.emotional_intelligence._update_psychological_profile(
                    user_id, emotional_insight
                )

        except Exception as e:
            logger.error(f"Background update error: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0

        return {
            'average_response_time': avg_response_time,
            'total_requests': self.total_requests,
            'cache_hit_rates': dict(self.cache_hit_rates),
            'active_conversations': len(self.active_conversations),
            'cache_sizes': {
                'response_cache': len(self.response_cache),
                'context_cache': len(self.context_cache),
                'memory_cache': len(self.memory_cache),
                'emotional_cache': len(self.emotional_cache)
            },
            'recent_response_times': list(self.response_times)[-10:]
        }

    async def warm_up_user(self, user_id: str):
        """Warm up caches for a specific user."""
        await self.preload_queue.put(user_id)

    def clear_caches(self):
        """Clear all caches (for memory management)."""
        with self.cache_lock:
            self.response_cache.clear()
            self.context_cache.clear()
            self.memory_cache.clear()
            self.emotional_cache.clear()
        logger.info("All caches cleared")

    async def shutdown(self):
        """Graceful shutdown."""
        if self.preload_task:
            self.preload_task.cancel()
        self.thread_pool.shutdown(wait=True)
        logger.info("Ultra-fast conversation service shutdown complete")
